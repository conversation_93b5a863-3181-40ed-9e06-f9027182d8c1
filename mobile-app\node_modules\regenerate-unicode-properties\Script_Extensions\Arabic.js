const set = require('regenerate')(0x204F, 0x2E41, 0xFDCF, 0x1EE24, 0x1EE27, 0x1EE39, 0x1EE3B, 0x1EE42, 0x1EE47, 0x1EE49, 0x1EE4B, 0x1EE54, 0x1EE57, 0x1EE59, 0x1EE5B, 0x1EE5D, 0x1EE5F, 0x1EE64, 0x1EE7E);
set.addRange(0x600, 0x604).addRange(0x606, 0x6DC).addRange(0x6DE, 0x6FF).addRange(0x750, 0x77F).addRange(0x870, 0x88E).addRange(0x890, 0x891).addRange(0x897, 0x8E1).addRange(0x8E3, 0x8FF).addRange(0xFB50, 0xFBC2).addRange(0xFBD3, 0xFD8F).addRange(0xFD92, 0xFDC7).addRange(0xFDF0, 0xFDFF).addRange(0xFE70, 0xFE74).addRange(0xFE76, 0xFEFC).addRange(0x102E0, 0x102FB).addRange(0x10E60, 0x10E7E).addRange(0x10EC2, 0x10EC4).addRange(0x10EFC, 0x10EFF).addRange(0x1EE00, 0x1EE03).addRange(0x1EE05, 0x1EE1F).addRange(0x1EE21, 0x1EE22).addRange(0x1EE29, 0x1EE32).addRange(0x1EE34, 0x1EE37).addRange(0x1EE4D, 0x1EE4F).addRange(0x1EE51, 0x1EE52).addRange(0x1EE61, 0x1EE62).addRange(0x1EE67, 0x1EE6A).addRange(0x1EE6C, 0x1EE72).addRange(0x1EE74, 0x1EE77).addRange(0x1EE79, 0x1EE7C).addRange(0x1EE80, 0x1EE89).addRange(0x1EE8B, 0x1EE9B).addRange(0x1EEA1, 0x1EEA3).addRange(0x1EEA5, 0x1EEA9).addRange(0x1EEAB, 0x1EEBB).addRange(0x1EEF0, 0x1EEF1);
exports.characters = set;
