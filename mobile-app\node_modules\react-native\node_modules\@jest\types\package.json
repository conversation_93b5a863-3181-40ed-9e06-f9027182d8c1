{"name": "@jest/types", "version": "26.6.2", "repository": {"type": "git", "url": "https://github.com/facebook/jest.git", "directory": "packages/jest-types"}, "engines": {"node": ">= 10.14.2"}, "license": "MIT", "main": "build/index.js", "types": "build/index.d.ts", "dependencies": {"@types/istanbul-lib-coverage": "^2.0.0", "@types/istanbul-reports": "^3.0.0", "@types/node": "*", "@types/yargs": "^15.0.0", "chalk": "^4.0.0"}, "publishConfig": {"access": "public"}, "gitHead": "4c46930615602cbf983fb7e8e82884c282a624d5"}