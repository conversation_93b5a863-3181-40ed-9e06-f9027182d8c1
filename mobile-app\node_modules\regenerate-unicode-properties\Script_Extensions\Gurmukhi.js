const set = require('regenerate')(0xA3C, 0xA51, 0xA5E);
set.addRange(0x951, 0x952).addRange(0x964, 0x965).addRange(0xA01, 0xA03).addRange(0xA05, 0xA0A).addRange(0xA0F, 0xA10).addRange(0xA13, 0xA28).addRange(0xA2A, 0xA30).addRange(0xA32, 0xA33).addRange(0xA35, 0xA36).addRange(0xA38, 0xA39).addRange(0xA3E, 0xA42).addRange(0xA47, 0xA48).addRange(0xA4B, 0xA4D).addRange(0xA59, 0xA5C).addRange(0xA66, 0xA76).addRange(0xA830, 0xA839);
exports.characters = set;
